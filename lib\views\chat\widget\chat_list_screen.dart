
import 'package:flutter/cupertino.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';

class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});
  static Widget builder(BuildContext context) {
    return const ChatListScreen();
  }

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  late ScrollController chatListscrollController;
  @override
  void initState() {
    super.initState();
    chatListscrollController = ScrollController()..addListener(_scrollListener);
    final state = context.read<ChatBloc>().state;
    if (state.chatList.isNotEmpty) {
      state.chatList.clear();
    }
    context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
  }

  @override
  void dispose() {
    chatListscrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (chatListscrollController.position.pixels ==
        chatListscrollController.position.maxScrollExtent) {
      final state = context.read<ChatBloc>().state;
      if (!state.isLoadingMore) {
        context.read<ChatBloc>().add(GetChatListEvent(page: state.page + 1));
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themestate) {
        return BlocBuilder<ChatBloc, ChatState>(
          builder: (context, state) {
            return Scaffold(
              resizeToAvoidBottomInset: false,
              appBar: RoomEightAppBar(
                showBackButton: false,
                // alignment: Alignment.center,
                title: 'Message',
                // title: Lang.of(context).lbl_messages,
              //   textStyle: Theme.of(context)
              //       .textTheme
              //       .titleLarge
              //       ?.copyWith(fontWeight: FontWeight.w700, fontSize: 18.sp),
              // ),
              body: Column(
                children: [
                  Expanded(
                    child:
                        //  state.isloding ? const Center(child: LoadingAnimationWidget()) :
                        _buildUserChatList(state),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewPadding.bottom),
                    child: Visibility(
                      visible: state.isLoadingMore,
                      child: SizedBox(
                        height: 50.h,
                        child: Center(
                            child: CupertinoActivityIndicator(
                                color: Theme.of(context).colorScheme.primary)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildUserChatList(ChatState state) {
    return UserChatListWidget(
      searchController: state.searchController ?? TextEditingController(),
      scrollController: chatListscrollController,
      chatList: state.chatList,
      showSearchField: true,
      onclose: () {
        final state = context.read<ChatBloc>().state;
        if (state.chatList.isNotEmpty) {
          state.chatList.clear();
        }
        context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
      },
    );
  }
}
