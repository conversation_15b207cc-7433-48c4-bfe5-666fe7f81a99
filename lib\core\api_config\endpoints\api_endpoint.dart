import 'package:room_eight/core/flavor_config/flavor_config.dart';

class ApiEndPoint {
  static String get baseUrl => FlavorConfig.instance.env.baseUrl;
  static String get getImageUrl => "https://room8.flexioninfotech.com";

  // Auth Endpoints
  static String get signUpUrl => '$baseUrl/register/';
  static String get loginUpUrl => '$baseUrl/login/';
  static String get createUserProfileUrl => '$baseUrl/create-user-profile/';

  // Profile Endpoints
  static String get getSelectionMenuUrl => "$baseUrl/get-selection-menu/";
  static String get setHabitsLifestyle => "$baseUrl/create-habits-lifestyle/";
  static String get getAllUserDataUrl => "$baseUrl/get-all-profile/";
  static String get likeUserProfile => "$baseUrl/like-profile/";
  static String get dislikeUserProfile => "$baseUrl/dislike-profile/";
  static String get getMoveOutProfiles => "$baseUrl/profile-like-outwards/";
  static String get getMoveInnProfiles => "$baseUrl/profile-like-inwards/";
  static String get getUserProfileById =>
      "$baseUrl/detailed-profile/?profile_id";
  static String get userProfileUrl => "$baseUrl/user-profile/";
  static String get editProfileUrl => "$baseUrl/edit-profile/";
  static String get acceptLikeUrl => "$baseUrl/accept-like-profile/";
}
