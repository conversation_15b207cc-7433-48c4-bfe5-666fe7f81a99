part of 'home_bloc.dart';

abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object> get props => [];
}

class LoadHomePage extends HomeEvent {}

class HomePageChanged extends HomeEvent {
  final int index;
  const HomePageChanged(this.index);

  @override
  List<Object> get props => [index];
}

class HomeResetToFirst extends HomeEvent {
  const HomeResetToFirst();
}

class AcceptUser extends HomeEvent {
  final int userId;
  const AcceptUser(this.userId);

  @override
  List<Object> get props => [userId];
}

class RejectUser extends HomeEvent {
  final int userId;
  const RejectUser(this.userId);

  @override
  List<Object> get props => [userId];
}

class StartPulseAnimation extends HomeEvent {}

class StopPulseAnimation extends HomeEvent {}

class CarouselPageChanged extends HomeEvent {
  final int index;
  const CarouselPageChanged(this.index);

  @override
  List<Object> get props => [index];
}

class CheckTutorialStatus extends HomeEvent {
  const CheckTutorialStatus();
}

class DismissTutorial extends HomeEvent {
  const DismissTutorial();
}

class InitializeSwipableController extends HomeEvent {
  const InitializeSwipableController();
}

class DisposeSwipableController extends HomeEvent {
  const DisposeSwipableController();
}

class GetUserProfileByID extends HomeEvent {
  final int userId;
  const GetUserProfileByID(this.userId);

  @override
  // List<Object> get props => [user];
  List<Object> get props => [userId];
}
