// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/like_model/like_model.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  final LikeRepository likeRepository;
  LikeBloc(this.likeRepository) : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
    on<LoadLikeScreenData>(_onLoadLikeAndDisLikeData);
    on<LoadMoveOutData>(_onLoadMoveOutData);
    on<LoadMoveInData>(_onLoadMoveInData);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  void _onLoadLikeAndDisLikeData(
    LoadLikeScreenData event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadData: true));
    LikeModel moveOut = await likeRepository.getMoveOutData();
    LikeModel moveIn = await likeRepository.getMoveInData();

    if (moveOut.status == true && moveIn.status == true) {
      final List<LikeAndDisLikeUsers> likedata = moveOut.data ?? [];
      final List<LikeAndDisLikeUsers> disLikedata = moveIn.data ?? [];

      emit(
        state.copyWith(
          moveInData: likedata,
          moveOutData: disLikedata,
          isLoadData: false,
        ),
      );
    }
  }

  void _onLoadMoveOutData(
    LoadMoveOutData event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadData: true));
    try {
      LikeModel moveOut = await likeRepository.getMoveOutData();

      if (moveOut.status == true) {
        final List<LikeAndDisLikeUsers> moveOutData = moveOut.data ?? [];

        emit(state.copyWith(moveOutData: moveOutData, isLoadData: false));
      } else {
        emit(state.copyWith(isLoadData: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadData: false));
    }
  }

  void _onLoadMoveInData(LoadMoveInData event, Emitter<LikeState> emit) async {
    emit(state.copyWith(isLoadData: true));
    try {
      LikeModel moveIn = await likeRepository.getMoveInData();

      if (moveIn.status == true) {
        final List<LikeAndDisLikeUsers> moveInData = moveIn.data ?? [];

        emit(state.copyWith(moveInData: moveInData, isLoadData: false));
      } else {
        emit(state.copyWith(isLoadData: false));
      }
    } catch (e) {
      emit(state.copyWith(isLoadData: false));
    }
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
