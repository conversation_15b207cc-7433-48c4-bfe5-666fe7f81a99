// ignore: depend_on_referenced_packages
import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/like_model/like_model.dart';
import 'package:room_eight/repository/like_repository/like_repository.dart';

part 'like_event.dart';
part 'like_state.dart';

class LikeBloc extends Bloc<LikeEvent, LikeState> {
  final PageController pageController = PageController();
  final LikeRepository likeRepository;
  LikeBloc(this.likeRepository) : super(LikeState()) {
    on<TabChangedEvent>(_onTabChangedEvent);
    on<LoadLikeScreenData>(_onLoadLikeAndDisLikeData);
  }
  void _onTabChangedEvent(TabChangedEvent event, Emitter<LikeState> emit) {
    pageController.jumpToPage(event.newIndex);
    emit(state.copyWith(curentTebIndex: event.newIndex));
  }

  void _onLoadLikeAndDisLikeData(
    LoadLikeScreenData event,
    Emitter<LikeState> emit,
  ) async {
    emit(state.copyWith(isLoadData: true));
    LikeModel moveOut = await likeRepository.getMoveOutData();
    LikeModel moveIn = await likeRepository.getMoveInData();

    if (moveOut.status == true && moveIn.status == true) {
      final List<LikeAndDisLikeUsers> likedata = moveOut.data ?? [];
      final List<LikeAndDisLikeUsers> disLikedata = moveIn.data ?? [];

      emit(
        state.copyWith(
          moveInData: likedata,
          moveOutData: disLikedata,
          isLoadData: false,
        ),
      );
    }
  }

  @override
  Future<void> close() {
    pageController.dispose();
    return super.close();
  }
}
