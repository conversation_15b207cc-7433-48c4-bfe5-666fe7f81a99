import 'package:flutter/material.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/views/chat/chat_screen.dart';
import 'package:room_eight/views/chat/widget/user_search_dialog.dart';

/// Dialog widget to allow users to start a chat with another user by entering their token/username
class Chat<PERSON>ithTokenDialog extends StatefulWidget {
  const ChatWithTokenDialog({super.key});

  @override
  State<ChatWithTokenDialog> createState() => _ChatWithTokenDialogState();
}

class _ChatWithTokenDialogState extends State<ChatWithTokenDialog> {
  final TextEditingController _tokenController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _tokenController.dispose();
    super.dispose();
  }

  void _startChat() {
    final token = _tokenController.text.trim();
    if (token.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a user token or username'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Close the dialog first
    Navigator.of(context).pop();

    // Start chat with the user token
    ChatScreen.startChatWithUserToken(context, token);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Start Chat with User'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Enter the username or token of the user you want to chat with:',
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 16),
          CustomTextInputField(
            controller: _tokenController,
            hintLabel: 'Enter username or token',
            context: context,
            enabled: !_isLoading,
            type: InputType.text,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _startChat,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Start Chat'),
        ),
      ],
    );
  }
}

/// Extension to easily show the chat with token dialog
extension ChatWithTokenDialogExtension on BuildContext {
  void showChatWithTokenDialog() {
    showDialog(
      context: this,
      builder: (context) => const ChatWithTokenDialog(),
    );
  }
}

/// Floating Action Button widget for starting chat with token
class ChatWithTokenFAB extends StatelessWidget {
  const ChatWithTokenFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => context.showChatWithTokenDialog(),
      backgroundColor: Theme.of(context).primaryColor,
      child: const Icon(Icons.person_add),
    );
  }
}

/// Floating Action Button widget for searching users
class UserSearchFAB extends StatelessWidget {
  const UserSearchFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => context.showUserSearchDialog(),
      backgroundColor: Theme.of(context).primaryColor,
      child: const Icon(Icons.search),
    );
  }
}

/// Simple button widget for starting chat with token
class ChatWithTokenButton extends StatelessWidget {
  final String? label;
  final IconData? icon;

  const ChatWithTokenButton({super.key, this.label, this.icon});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => context.showChatWithTokenDialog(),
      icon: Icon(icon ?? Icons.person_add),
      label: Text(label ?? 'Chat with User'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }
}
