# Chat with User Token - Usage Guide

This guide explains how to implement and use the "Chat with User Token" functionality in the Room Eight app.

## Overview

The chat with token functionality allows users to start a chat conversation with another user by entering their username or token. The system searches for the user and automatically opens a chat screen if the user is found.

## Implementation

### Core Functionality

The main functionality is implemented in `ChatScreen.startChatWithUserToken()` method:

```dart
ChatScreen.startChatWithUserToken(context, userToken);
```

### Components Added

1. **ChatScreen.startChatWithUserToken()** - Static method in `lib/views/chat/chat_screen.dart`
2. **ChatWithTokenDialog** - Dialog widget in `lib/views/chat/widget/chat_with_token_dialog.dart`
3. **ChatWithTokenFAB** - Floating Action Button widget
4. **ChatWithTokenButton** - Simple button widget
5. **ChatTokenExampleScreen** - Example implementation screen

## Usage Examples

### Method 1: Using the Dialog

```dart
import 'package:room_eight/views/chat/widget/chat_with_token_dialog.dart';

// Show dialog using extension method
context.showChatWithTokenDialog();

// Or show dialog manually
showDialog(
  context: context,
  builder: (context) => const ChatWithTokenDialog(),
);
```

### Method 2: Using Floating Action Button

```dart
import 'package:room_eight/views/chat/widget/chat_with_token_dialog.dart';

Scaffold(
  floatingActionButton: const ChatWithTokenFAB(),
  // ... rest of your scaffold
)
```

### Method 3: Using Button Widget

```dart
import 'package:room_eight/views/chat/widget/chat_with_token_dialog.dart';

const ChatWithTokenButton(
  label: 'Chat with User',
  icon: Icons.person_add,
)
```

### Method 4: Direct Method Call

```dart
import 'package:room_eight/views/chat/chat_screen.dart';

void startChat(String userToken) {
  ChatScreen.startChatWithUserToken(context, userToken);
}
```

## How It Works

1. **User Input**: User enters a username or token
2. **Search**: The app uses `SearchUserListEvent` to search for the user via socket
3. **Loading**: A loading dialog is shown while searching
4. **Result Handling**:
   - If user found: Navigate to chat screen with the user
   - If user not found: Show error message
5. **Chat**: User can immediately start chatting with the found user

## Integration Examples

### Adding to Chat List Screen

The chat list screen has been updated to include a floating action button:

```dart
// In lib/views/chat/widget/chat_list_screen.dart
Scaffold(
  floatingActionButton: const ChatWithTokenFAB(),
  // ... rest of scaffold
)
```

### Adding to Any Screen

You can add the functionality to any screen:

```dart
// Add as a button in app bar
AppBar(
  actions: [
    IconButton(
      icon: const Icon(Icons.person_add),
      onPressed: () => context.showChatWithTokenDialog(),
    ),
  ],
)

// Add as a menu item
ListTile(
  leading: const Icon(Icons.chat),
  title: const Text('Chat with User'),
  onTap: () => context.showChatWithTokenDialog(),
)
```

## Customization

### Custom Dialog

You can create your own dialog by copying and modifying `ChatWithTokenDialog`:

```dart
class CustomChatDialog extends StatefulWidget {
  // Your custom implementation
}
```

### Custom Button Styling

```dart
ChatWithTokenButton(
  label: 'Start New Chat',
  icon: Icons.message,
)
```

### Custom FAB

```dart
FloatingActionButton.extended(
  onPressed: () => context.showChatWithTokenDialog(),
  icon: const Icon(Icons.person_add),
  label: const Text('New Chat'),
)
```

## Error Handling

The implementation includes proper error handling:

- **Empty Input**: Shows warning if no token is entered
- **User Not Found**: Shows error message if user doesn't exist
- **Network Issues**: Handled by the underlying socket implementation
- **Memory Leaks**: Subscription is automatically cancelled after 10 seconds

## Testing

To test the functionality:

1. Run the app
2. Navigate to the chat list screen
3. Tap the floating action button (person_add icon)
4. Enter a valid username or token
5. Tap "Start Chat"
6. Verify that the chat screen opens if user is found

## Example Screen

A complete example implementation is available in:
`lib/views/chat/widget/chat_token_example_screen.dart`

This screen demonstrates all the different ways to use the chat with token functionality.

## Dependencies

The functionality uses existing app components:
- Socket service for user search
- Chat bloc for state management
- Navigation service for routing
- Existing chat screen for the conversation

No additional dependencies are required.

## Notes

- The search is performed using the existing `SearchUserListEvent` 
- The functionality integrates seamlessly with the existing chat system
- Users found via token work exactly like users from the regular chat list
- The implementation follows the app's existing patterns and conventions
