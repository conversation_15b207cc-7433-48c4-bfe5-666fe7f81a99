part of 'like_bloc.dart';

class LikeState extends Equatable {
  final int curentTebIndex;
  final List<LikeAndDisLikeUsers> moveInData;
  final List<LikeAndDisLikeUsers> moveOutData;
  final bool isLoadData;
  final bool isLoadingMoveOut;
  final bool isLoadingMoveIn;

  const LikeState({
    this.curentTebIndex = 0,
    this.isLoadData = false,
    this.isLoadingMoveOut = false,
    this.isLoadingMoveIn = false,
    this.moveInData = const [],
    this.moveOutData = const [],
  });

  @override
  List<Object> get props => [
    curentTebIndex,
    isLoadData,
    isLoadingMoveOut,
    isLoadingMoveIn,
    moveInData,
    moveOutData,
  ];

  LikeState copyWith({
    final int? curentTebIndex,
    final bool? isLoadData,
    final bool? isLoadingMoveOut,
    final bool? isLoadingMoveIn,
    final List<LikeAndDisLikeUsers>? moveInData,
    final List<LikeAndDisLikeUsers>? moveOutData,
  }) {
    return LikeState(
      curentTebIndex: curentTebIndex ?? this.curentTebIndex,
      isLoadData: isLoadData ?? this.isLoadData,
      isLoadingMoveOut: isLoadingMoveOut ?? this.isLoadingMoveOut,
      isLoadingMoveIn: isLoadingMoveIn ?? this.isLoadingMoveIn,
      moveInData: moveInData ?? this.moveInData,
      moveOutData: moveOutData ?? this.moveOutData,
    );
  }
}
