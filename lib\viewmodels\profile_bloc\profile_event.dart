part of 'profile_bloc.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class LeasePeriodChanged extends ProfileEvent {
  final String leasePeriod;
  const LeasePeriodChanged(this.leasePeriod);

  @override
  List<Object?> get props => [leasePeriod];
}

class AddPhoto extends ProfileEvent {
  final String photoPath;
  const AddPhoto(this.photoPath);

  @override
  List<Object?> get props => [photoPath];
}

class RemovePhoto extends ProfileEvent {
  final String photoPath;
  const RemovePhoto(this.photoPath);

  @override
  List<Object?> get props => [photoPath];
}

class ProfileImageChanged extends ProfileEvent {
  final String imagePath;
  const ProfileImageChanged(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

class NameChanged extends ProfileEvent {
  final String name;
  const NameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class EmailChanged extends ProfileEvent {
  final String email;
  const EmailChanged(this.email);

  @override
  List<Object?> get props => [email];
}

class GenderChanged extends ProfileEvent {
  final String selectedGender;
  const GenderChanged(this.selectedGender);

  @override
  List<Object?> get props => [selectedGender];
}

class PreferredGenderChanged extends ProfileEvent {
  final String preferredGender;
  const PreferredGenderChanged(this.preferredGender);

  @override
  List<Object?> get props => [preferredGender];
}

class SmokingPreferenceChanged extends ProfileEvent {
  final String smokingPreference;
  const SmokingPreferenceChanged(this.smokingPreference);

  @override
  List<Object?> get props => [smokingPreference];
}

class CleanlinessChanged extends ProfileEvent {
  final String cleanliness;
  const CleanlinessChanged(this.cleanliness);

  @override
  List<Object?> get props => [cleanliness];
}

class PetPreferenceChanged extends ProfileEvent {
  final String petPreference;
  const PetPreferenceChanged(this.petPreference);

  @override
  List<Object?> get props => [petPreference];
}

class ClassStandingChanged extends ProfileEvent {
  final String classStanding;
  const ClassStandingChanged(this.classStanding);

  @override
  List<Object?> get props => [classStanding];
}

class AgeChanged extends ProfileEvent {
  final String age;
  const AgeChanged(this.age);

  @override
  List<Object?> get props => [age];
}

class DobChanged extends ProfileEvent {
  final DateTime dob;
  const DobChanged(this.dob);

  @override
  List<Object?> get props => [dob];
}

class AddPersonalityTag extends ProfileEvent {
  final String tag;
  const AddPersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class RemovePersonalityTag extends ProfileEvent {
  final String tag;
  const RemovePersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class AddCustomPersonalityTag extends ProfileEvent {
  final String tag;
  const AddCustomPersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class SaveProfile extends ProfileEvent {
  const SaveProfile();
}

class FullProfileNameChanged extends ProfileEvent {
  final String fullName;
  const FullProfileNameChanged(this.fullName);
}

class ProfileDateOfBirthChanged extends ProfileEvent {
  final String dateOfBirth;
  const ProfileDateOfBirthChanged(this.dateOfBirth);
}

class ProfileSubmitted extends ProfileEvent {}

class FinalProfileSubmitted extends ProfileEvent {}

class SelectUserProfile extends ProfileEvent {}

class SelectPeriod extends ProfileEvent {
  final String period;

  const SelectPeriod({required this.period});
}

class SelectGender extends ProfileEvent {
  final String gender;

  const SelectGender({required this.gender});
}

class SelectPreferredGender extends ProfileEvent {
  final String preferredGender;

  const SelectPreferredGender({required this.preferredGender});
}

class SelectSmokingPerson extends ProfileEvent {
  final String type;

  const SelectSmokingPerson({required this.type});
}

class SelectCleanLevel extends ProfileEvent {
  final String level;

  const SelectCleanLevel({required this.level});
}

class SelectPet extends ProfileEvent {
  final String pet;

  const SelectPet({required this.pet});
}

class SelectClassStand extends ProfileEvent {
  final String classStand;

  const SelectClassStand({required this.classStand});
}

class SelectHabitsAndLifestyle extends ProfileEvent {
  final String habitsAndLifestyle;

  const SelectHabitsAndLifestyle({required this.habitsAndLifestyle});
}

class SelectCleanlinessLivingStyle extends ProfileEvent {
  final String cleanlinessLivingStyle;

  const SelectCleanlinessLivingStyle({required this.cleanlinessLivingStyle});
}

class SelectInterestsHobbies extends ProfileEvent {
  final String interestsHobbies;

  const SelectInterestsHobbies({required this.interestsHobbies});
}

class TogglePickThings extends ProfileEvent {}

class SearchChanged extends ProfileEvent {
  final String searchText;
  const SearchChanged(this.searchText);
}

class SelectMultiplePhotos extends ProfileEvent {
  final List<String> photoPaths;
  const SelectMultiplePhotos(this.photoPaths);

  @override
  List<Object?> get props => [photoPaths];
}

class ContactNumberChanged extends ProfileEvent {
  final String contactNumber;
  const ContactNumberChanged(this.contactNumber);
}

class AboutChanged extends ProfileEvent {
  final String about;
  const AboutChanged(this.about);

  @override
  List<Object?> get props => [about];
}

class SelectPreferredLocations extends ProfileEvent {
  final String locations;
  const SelectPreferredLocations({required this.locations});
}

class AddLocation extends ProfileEvent {
  final LocationModel location;
  const AddLocation(this.location);
}

class RemoveLocation extends ProfileEvent {
  final LocationModel location;
  const RemoveLocation(this.location);
}

class GetSelectionOption extends ProfileEvent {}

class GetUserProfile extends ProfileEvent {}

class EditProfile extends ProfileEvent {}
