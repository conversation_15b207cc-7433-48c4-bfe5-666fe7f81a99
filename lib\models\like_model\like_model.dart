class LikeModel {
  bool? status;
  String? message;
  List<LikeAndDisLikeUsers>? data;

  LikeModel({this.status, this.message, this.data});

  factory LikeModel.fromJson(Map<String, dynamic> json) {
    return LikeModel(
      status: json['status'],
      message: json['message'],
      data: json['data'] != null
          ? List<LikeAndDisLikeUsers>.from(
              json['data'].map((item) => LikeAndDisLikeUsers.fromJson(item)),
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'data': data?.map((item) => item.toJson()).toList(),
    };
  }
}

class LikeAndDisLikeUsers {
  int? id;
  String? name;
  String? year;
  bool? status;

  LikeAndDisLikeUsers({this.id, this.name, this.year, this.status});

  factory LikeAndDisLikeUsers.fromJson(Map<String, dynamic> json) {
    return LikeAndDisLikeUsers(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      year: json['year'] ?? '',
      status: json['status'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'year': year, 'status': status};
  }
}
